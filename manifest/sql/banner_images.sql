-- Banner图片表
create table islamic_content_svc.banner_images
(
    id                int unsigned auto_increment comment '主键ID'
        primary key,
    banner_language_id int unsigned     default '0'  not null comment 'Banner多语言表ID',
    image_url         varchar(500)     default ''   not null comment '图片URL',
    link_url          varchar(500)     default ''   not null comment '跳转链接URL，可为空',
    sort_order        int unsigned     default '0'  not null comment '图片排序，数字越小越靠前',
    create_time       bigint unsigned  default '0'  not null comment '创建时间(毫秒时间戳)',
    update_time       bigint unsigned  default '0'  not null comment '更新时间(毫秒时间戳)'
)
    comment 'Banner图片表' collate = utf8mb4_unicode_ci;

-- banner图片表索引
create index idx_banner_language_sort
    on islamic_content_svc.banner_images (banner_language_id, sort_order);

create index idx_banner_language_id
    on islamic_content_svc.banner_images (banner_language_id);
