-- Banner表结构修改脚本
-- 执行前请确保已备份数据

-- 1. 创建广告位置表
create table islamic_content_svc.banner_position
(
    id            int unsigned auto_increment comment '主键ID'
        primary key,
    position_name varchar(100)     default ''  not null comment '广告位名称',
    position_code varchar(50)      default ''  not null comment '位置编码，自动生成',
    remark        varchar(500)     default ''  not null comment '备注，可修改',
    create_time   bigint unsigned  default '0' not null comment '创建时间(毫秒时间戳)',
    update_time   bigint unsigned  default '0' not null comment '更新时间(毫秒时间戳)'
)
    comment '广告位置表' collate = utf8mb4_unicode_ci;

-- 广告位置表索引
create unique index uk_position_code
    on islamic_content_svc.banner_position (position_code);

create index idx_create_time
    on islamic_content_svc.banner_position (create_time desc);

-- 初始化广告位置数据
INSERT INTO islamic_content_svc.banner_position (position_name, position_code, remark, create_time, update_time) VALUES
('首页广告位', 'home', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('礼拜广告位', 'ibadah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 2. 创建广告表
create table islamic_content_svc.advertisement
(
    id            int unsigned auto_increment comment '主键ID'
        primary key,
    position_code varchar(20)      default 'home' not null comment '广告位置编码: home, ibadah',
    ad_name       varchar(255)     default ''     not null comment '广告名称',
    display_type  tinyint unsigned default '1'    not null comment '显示类型: 1-单图固定, 2-多图轮播',
    interval_time int unsigned     default '3'    not null comment '轮播间隔时间(秒)，仅多图轮播时有效',
    sort_order    int unsigned     default '0'    not null comment '排序权重，数字越小越靠前',
    status        tinyint unsigned default '1'    not null comment '状态: 0-禁用, 1-启用',
    admin_id      int unsigned     default '0'    not null comment '创建管理员ID',
    create_time   bigint unsigned  default '0'    not null comment '创建时间(毫秒时间戳)',
    update_time   bigint unsigned  default '0'    not null comment '更新时间(毫秒时间戳)'
)
    comment '广告表' collate = utf8mb4_unicode_ci;

-- 广告表索引
create index idx_position_status_sort
    on islamic_content_svc.advertisement (position_code, status, sort_order);

-- 3. 修改banner表，添加advertisement_id字段
ALTER TABLE islamic_content_svc.banner
ADD COLUMN advertisement_id int unsigned default '0' not null comment '所属广告ID' AFTER id;

-- 4. 数据迁移：为每个banner创建对应的广告，并更新advertisement_id
-- 使用临时变量来处理数据迁移
SET @ad_id = 0;

-- 为每个banner记录创建对应的广告
INSERT INTO islamic_content_svc.advertisement
(position_code, ad_name, sort_order, status, admin_id, create_time, update_time)
SELECT
    banner_type as position_code,
    CONCAT('广告-', title) as ad_name,
    sort_order,
    status,
    admin_id,
    create_time,
    update_time
FROM islamic_content_svc.banner
ORDER BY id;

-- 5. 更新banner表的advertisement_id（按顺序匹配）
UPDATE islamic_content_svc.banner b
JOIN (
    SELECT
        ROW_NUMBER() OVER (ORDER BY id) as rn,
        id as banner_id
    FROM islamic_content_svc.banner
) b_rn ON b.id = b_rn.banner_id
JOIN (
    SELECT
        ROW_NUMBER() OVER (ORDER BY id) as rn,
        id as ad_id
    FROM islamic_content_svc.advertisement
) a_rn ON b_rn.rn = a_rn.rn
SET b.advertisement_id = a_rn.ad_id;

-- 6. 修改banner表，移除冗余字段（这些字段现在在advertisement表中管理）
ALTER TABLE islamic_content_svc.banner
DROP COLUMN banner_type,
DROP COLUMN sort_order,
DROP COLUMN status,
DROP COLUMN admin_id;

-- 7. 删除旧索引
DROP INDEX idx_banner_type ON islamic_content_svc.banner;
DROP INDEX idx_language_type_status_sort_order ON islamic_content_svc.banner;

-- 8. 创建新索引
create index idx_advertisement_language
    on islamic_content_svc.banner (advertisement_id, language_id);

create index idx_advertisement_id
    on islamic_content_svc.banner (advertisement_id);
