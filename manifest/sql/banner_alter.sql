-- Banner表结构修改脚本
-- 执行前请确保已备份数据

-- 1. 创建广告位置表
create table islamic_content_svc.banner_position
(
    id            int unsigned auto_increment comment '主键ID'
        primary key,
    position_name varchar(100)     default ''  not null comment '广告位名称',
    position_code varchar(50)      default ''  not null comment '位置编码，自动生成',
    remark        varchar(500)     default ''  not null comment '备注，可修改',
    create_time   bigint unsigned  default '0' not null comment '创建时间(毫秒时间戳)',
    update_time   bigint unsigned  default '0' not null comment '更新时间(毫秒时间戳)'
)
    comment '广告位置表' collate = utf8mb4_unicode_ci;

-- 广告位置表索引
create unique index uk_position_code
    on islamic_content_svc.banner_position (position_code);

create index idx_create_time
    on islamic_content_svc.banner_position (create_time desc);

-- 初始化广告位置数据
INSERT INTO islamic_content_svc.banner_position (position_name, position_code, remark, create_time, update_time) VALUES
('首页广告位', 'home', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('礼拜广告位', 'ibadah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 2. 创建banner多语言表
create table islamic_content_svc.banner_languages
(
    id             int unsigned auto_increment comment '主键ID'
        primary key,
    banner_id      int unsigned     default '0'      not null comment 'Banner主表ID',
    language_id    tinyint unsigned default '0'      not null comment '语言ID: 0-中文, 1-英文, 2-印尼语',
    title          varchar(255)     default ''       not null comment '广告标题',
    description    text                              null comment '广告描述',
    display_type   tinyint unsigned default '1'      not null comment '显示类型: 1-单图固定, 2-多图轮播',
    interval_time  int unsigned     default '3'      not null comment '轮播间隔时间(秒)，仅多图轮播时有效',
    create_time    bigint unsigned  default '0'      not null comment '创建时间(毫秒时间戳)',
    update_time    bigint unsigned  default '0'      not null comment '更新时间(毫秒时间戳)'
)
    comment 'Banner多语言表' collate = utf8mb4_unicode_ci;

-- banner多语言表索引
create unique index uk_banner_language
    on islamic_content_svc.banner_languages (banner_id, language_id);

create index idx_banner_id
    on islamic_content_svc.banner_languages (banner_id);

create index idx_language_id
    on islamic_content_svc.banner_languages (language_id);

-- 3. 创建banner图片表
create table islamic_content_svc.banner_images
(
    id                int unsigned auto_increment comment '主键ID'
        primary key,
    banner_language_id int unsigned     default '0'  not null comment 'Banner多语言表ID',
    image_url         varchar(500)     default ''   not null comment '图片URL',
    link_url          varchar(500)     default ''   not null comment '跳转链接URL，可为空',
    sort_order        int unsigned     default '0'  not null comment '图片排序，数字越小越靠前',
    create_time       bigint unsigned  default '0'  not null comment '创建时间(毫秒时间戳)',
    update_time       bigint unsigned  default '0'  not null comment '更新时间(毫秒时间戳)'
)
    comment 'Banner图片表' collate = utf8mb4_unicode_ci;

-- banner图片表索引
create index idx_banner_language_sort
    on islamic_content_svc.banner_images (banner_language_id, sort_order);

create index idx_banner_language_id
    on islamic_content_svc.banner_images (banner_language_id);

-- 4. 数据迁移：将现有banner表的多语言数据迁移到新表
INSERT INTO islamic_content_svc.banner_languages
(banner_id, language_id, title, description, create_time, update_time)
SELECT
    id as banner_id,
    language_id,
    title,
    description,
    create_time,
    update_time
FROM islamic_content_svc.banner;

-- 5. 数据迁移：将现有banner表的图片数据迁移到图片表
INSERT INTO islamic_content_svc.banner_images
(banner_language_id, image_url, link_url, sort_order, create_time, update_time)
SELECT
    bl.id as banner_language_id,
    b.image_url,
    b.link_url,
    0 as sort_order,
    b.create_time,
    b.update_time
FROM islamic_content_svc.banner b
JOIN islamic_content_svc.banner_languages bl ON b.id = bl.banner_id AND b.language_id = bl.language_id
WHERE b.image_url != '';

-- 6. 修改banner主表，移除多语言相关字段
ALTER TABLE islamic_content_svc.banner
DROP COLUMN language_id,
DROP COLUMN title,
DROP COLUMN description,
DROP COLUMN image_url,
DROP COLUMN link_url;

-- 7. 删除包含language_id的索引
DROP INDEX idx_language_type_status_sort_order ON islamic_content_svc.banner;

-- 8. 重新创建索引
create index idx_type_status_sort_order
    on islamic_content_svc.banner (banner_type, status, sort_order);
