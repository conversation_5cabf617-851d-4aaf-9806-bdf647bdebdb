-- 广告表
create table islamic_content_svc.advertisement
(
    id            int unsigned auto_increment comment '主键ID'
        primary key,
    position_code varchar(20)      default 'home' not null comment '广告位置编码: home, ibadah',
    ad_name       varchar(255)     default ''     not null comment '广告名称',
    display_type  tinyint unsigned default '1'    not null comment '显示类型: 1-单图固定, 2-多图轮播',
    interval_time int unsigned     default '3'    not null comment '轮播间隔时间(秒)，仅多图轮播时有效',
    sort_order    int unsigned     default '0'    not null comment '排序权重，数字越小越靠前',
    status        tinyint unsigned default '1'    not null comment '状态: 0-禁用, 1-启用',
    start_time    bigint unsigned  default '0'    not null comment '开始时间戳(毫秒)',
    end_time      bigint unsigned  default '0'    not null comment '结束时间戳(毫秒)',
    admin_id      int unsigned     default '0'    not null comment '创建管理员ID',
    create_time   bigint unsigned  default '0'    not null comment '创建时间(毫秒时间戳)',
    update_time   bigint unsigned  default '0'    not null comment '更新时间(毫秒时间戳)'
)
    comment '广告表' collate = utf8mb4_unicode_ci;

-- 广告表索引
create index idx_position_status_sort
    on islamic_content_svc.advertisement (position_code, status, sort_order);

create index idx_status_time
    on islamic_content_svc.advertisement (status, start_time, end_time);

create index idx_time_range
    on islamic_content_svc.advertisement (start_time, end_time);
