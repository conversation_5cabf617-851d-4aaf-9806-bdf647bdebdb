-- Banner多语言表
create table islamic_content_svc.banner_languages
(
    id             int unsigned auto_increment comment '主键ID'
        primary key,
    banner_id      int unsigned     default '0'      not null comment 'Banner主表ID',
    language_id    tinyint unsigned default '0'      not null comment '语言ID: 0-中文, 1-英文, 2-印尼语',
    title          varchar(255)     default ''       not null comment '广告标题',
    description    text                              null comment '广告描述',
    display_type   tinyint unsigned default '1'      not null comment '显示类型: 1-单图固定, 2-多图轮播',
    interval_time  int unsigned     default '3'      not null comment '轮播间隔时间(秒)，仅多图轮播时有效',
    images         json                              null comment '图片信息JSON数组，包含image_url和link_url',
    create_time    bigint unsigned  default '0'      not null comment '创建时间(毫秒时间戳)',
    update_time    bigint unsigned  default '0'      not null comment '更新时间(毫秒时间戳)'
)
    comment 'Banner多语言表' collate = utf8mb4_unicode_ci;

-- banner多语言表索引
create unique index uk_banner_language
    on islamic_content_svc.banner_languages (banner_id, language_id);

create index idx_banner_id
    on islamic_content_svc.banner_languages (banner_id);

create index idx_language_id
    on islamic_content_svc.banner_languages (language_id);
