-- 广告位置表
create table islamic_content_svc.banner_position
(
    id            int unsigned auto_increment comment '主键ID'
        primary key,
    position_name varchar(100)     default ''  not null comment '广告位名称',
    position_code varchar(50)      default ''  not null comment '位置编码，自动生成',
    remark        varchar(500)     default ''  not null comment '备注，可修改',
    create_time   bigint unsigned  default '0' not null comment '创建时间(毫秒时间戳)',
    update_time   bigint unsigned  default '0' not null comment '更新时间(毫秒时间戳)'
)
    comment '广告位置表' collate = utf8mb4_unicode_ci;

-- 广告位置表索引
create unique index uk_position_code
    on islamic_content_svc.banner_position (position_code);

create index idx_create_time
    on islamic_content_svc.banner_position (create_time desc);

-- 初始化广告位置数据
INSERT INTO islamic_content_svc.banner_position (position_name, position_code, remark, create_time, update_time) VALUES
('首页广告位', 'home', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('礼拜广告位', 'ibadah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);
