-- 修改后的Banner表结构（单张图片）
create table islamic_content_svc.banner
(
    id              int unsigned auto_increment comment '主键ID'
        primary key,
    advertisement_id int unsigned     default '0'  not null comment '所属广告ID',
    language_id     tinyint unsigned default '0'  not null comment '语言ID: 0-中文, 1-英文, 2-印尼语',
    title           varchar(255)     default ''   not null comment '广告标题',
    description     text                          null comment '广告描述',
    image_url       varchar(500)     default ''   not null comment '广告图片URL',
    link_url        varchar(500)     default ''   not null comment '跳转链接URL',
    create_time     bigint unsigned  default '0'  not null comment '创建时间(毫秒时间戳)',
    update_time     bigint unsigned  default '0'  not null comment '更新时间(毫秒时间戳)'
)
    comment 'Banner图片表' collate = utf8mb4_unicode_ci;

-- banner表索引
create index idx_advertisement_language
    on islamic_content_svc.banner (advertisement_id, language_id);

create index idx_advertisement_id
    on islamic_content_svc.banner (advertisement_id);
