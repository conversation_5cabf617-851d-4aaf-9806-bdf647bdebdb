package message

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func SetDefaultPage(listReq *v1.ListReq) v1.ListReq {
	if listReq == nil {
		return v1.ListReq{
			Current:  1,
			PageSize: 10,
		}
	}
	if listReq.Current == 0 {
		listReq.Current = 1
	}
	if listReq.PageSize == 0 {
		listReq.PageSize = 10
	}
	return *listReq
}
func (c *Controller) MessageList(ctx context.Context, req *v1.MessageListReq) (res *v1.MessageListRes, err error) {
	req.ListReq = SetDefaultPage(&req.ListReq)
	res, err = service.Message().MessageList(ctx, req)
	return
}

func (c *Controller) MessageOne(ctx context.Context, req *v1.MessageOneReq) (res *v1.MessageOneRes, err error) {
	res, err = service.Message().MessageOne(ctx, req)
	return
}

func (c *Controller) MessageEdit(ctx context.Context, req *v1.MessageEditReq) (res *v1.MessageEditRes, err error) {
	res, err = service.Message().MessageEdit(ctx, req)
	return
}

func (c *Controller) MessageAdd(ctx context.Context, req *v1.MessageCreateReq) (res *v1.MessageCreateRes, err error) {
	res, err = service.Message().Add(ctx, req)
	return
}

func (c *Controller) MessageDelete(ctx context.Context, req *v1.MessageDeleteReq) (res *v1.MessageDeleteRes, err error) {
	res, err = service.Message().MessageDelete(ctx, req)
	return
}
