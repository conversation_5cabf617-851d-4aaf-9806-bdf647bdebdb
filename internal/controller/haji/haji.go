package haji

import (
	"context"
	"gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 获取 Urutan Manasik 列表
func (c *Controller) UrutanManasikList(ctx context.Context, req *v1.UrutanManasikListReq) (res *v1.UrutanManasikListRes, err error) {
	res = new(v1.UrutanManasikListRes)
	res, err = service.Haji().UrutanManasikList(ctx, req)
	return
}

// 获取 Urutan Manasik 详情
func (c *Controller) UrutanManasikInfo(ctx context.Context, req *v1.UrutanManasikInfoReq) (res *v1.UrutanManasikInfoRes, err error) {
	res = new(v1.UrutanManasikInfoRes)
	res, err = service.Haji().UrutanManasikInfo(ctx, req)
	return
}

// 新增 Urutan Manasik
func (c *Controller) UrutanManasikAdd(ctx context.Context, req *v1.UrutanManasikAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Haji().UrutanManasikAdd(ctx, req)
	return
}

// 编辑 Urutan Manasik
func (c *Controller) UrutanManasikEdit(ctx context.Context, req *v1.UrutanManasikEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Haji().UrutanManasikEdit(ctx, req)
	return
}

// Ringkas 列表
func (c *Controller) DoaRingkasHajiList(ctx context.Context, req *v1.DoaRingkasHajiListReq) (res *v1.DoaRingkasHajiListRes, err error) {
	res = new(v1.DoaRingkasHajiListRes)
	res, err = service.Haji().DoaRingkasHajiList(ctx, req)
	return
}

// Ringkas 详情
func (c *Controller) DoaRingkasHajiInfo(ctx context.Context, req *v1.DoaRingkasHajiInfoReq) (res *v1.DoaRingkasHajiInfoRes, err error) {
	res = new(v1.DoaRingkasHajiInfoRes)
	res, err = service.Haji().DoaRingkasHajiInfo(ctx, req)
	return
}

// Panjang 列表
func (c *Controller) DoaPanjangHajiList(ctx context.Context, req *v1.DoaPanjangHajiListReq) (res *v1.DoaPanjangHajiListRes, err error) {
	res = new(v1.DoaPanjangHajiListRes)
	res, err = service.Haji().DoaPanjangHajiList(ctx, req)
	return
}

// Panjang Bacaan列表
func (c *Controller) DoaPanjangHajiBacaanList(ctx context.Context, req *v1.DoaPanjangHajiBacaanListReq) (res *v1.DoaPanjangHajiBacaanListRes, err error) {
	res = new(v1.DoaPanjangHajiBacaanListRes)
	res, err = service.Haji().DoaPanjangHajiBacaanList(ctx, req)
	return
}

// Panjang Bacaan列表详情
func (c *Controller) DoaPanjangHajiBacaanListInfo(ctx context.Context, req *v1.DoaPanjangHajiBacaanListInfoReq) (res *v1.DoaPanjangHajiBacaanListInfoRes, err error) {
	res = new(v1.DoaPanjangHajiBacaanListInfoRes)
	res, err = service.Haji().DoaPanjangHajiBacaanListInfo(ctx, req)
	return
}

// Hikmah Haji列表
func (c *Controller) HikmahHajiList(ctx context.Context, req *v1.HikmahHajiListReq) (res *v1.HikmahHajiListRes, err error) {
	res = new(v1.HikmahHajiListRes)
	res, err = service.Haji().HikmahHajiList(ctx, req)
	return
}

// Hikmah Haji详情
func (c *Controller) HikmahHajiInfo(ctx context.Context, req *v1.HikmahHajiInfoReq) (res *v1.HikmahHajiInfoRes, err error) {
	res = new(v1.HikmahHajiInfoRes)
	res, err = service.Haji().HikmahHajiInfo(ctx, req)
	return
}

// Hikmah Haji新增
func (c *Controller) HikmahHajiAdd(ctx context.Context, req *v1.HikmahHajiAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Haji().HikmahHajiAdd(ctx, req)
	return
}

// Hikmah Haji编辑
func (c *Controller) HikmahHajiEdit(ctx context.Context, req *v1.HikmahHajiEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Haji().HikmahHajiEdit(ctx, req)
	return
}

// Hikmah Haji删除
func (c *Controller) HikmahHajiDelete(ctx context.Context, req *v1.HikmahHajiDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	res, err = service.Haji().HikmahHajiDelete(ctx, req)
	return
}
