// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserAddress is the golang structure for table user_address.
type UserAddress struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	UserId     uint64 `json:"userId"     orm:"user_id"     description:"用户ID"`
	Level1Code string `json:"level1Code" orm:"level1_code" description:"省级代码"`
	Level1Name string `json:"level1Name" orm:"level1_name" description:"省名称"`
	Level2Code string `json:"level2Code" orm:"level2_code" description:"市级代码"`
	Level2Name string `json:"level2Name" orm:"level2_name" description:"市名称"`
	Level3Code string `json:"level3Code" orm:"level3_code" description:"区/镇代码"`
	Level3Name string `json:"level3Name" orm:"level3_name" description:"区/镇名称"`
	Level4Code string `json:"level4Code" orm:"level4_code" description:"村/社区代码"`
	Level4Name string `json:"level4Name" orm:"level4_name" description:"村/社区名称"`
	Address    string `json:"address"    orm:"address"     description:"详细地址，如门牌号、街道名"`
	AreaCode   string `json:"areaCode"   orm:"area_code"   description:"手机国际区号，如：86"`
	PhoneNum   string `json:"phoneNum"   orm:"phone_num"   description:"手机号"`
	Receiver   string `json:"receiver"   orm:"receiver"    description:"收货人姓名"`
	IsDefault  int    `json:"isDefault"  orm:"is_default"  description:"是否默认地址"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间，0代表创建后未被修改过"`
}
