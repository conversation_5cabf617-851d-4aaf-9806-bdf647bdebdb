package haji

import (
	"context"
	"database/sql"
	"errors"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
)

type sHaji struct{}

func init() {
	service.RegisterHaji(New())
}

func New() *sHaji {
	return &sHaji{}
}

// 新增朝觐
func (s *sHaji) UrutanManasikAdd(ctx context.Context, req *v1.UrutanManasikAddReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if "" == req.IconUrl {
		return out, gerror.New("图标不能为空")
	}
	if 0 >= req.UrutanNo {
		return out, gerror.New("朝觐顺序不能为空")
	}
	if 0 >= len(req.ContentArr) {
		return out, gerror.New("内容不能为空")
	}
	for _, item := range req.ContentArr {
		if "" == item.UrutanName {
			return out, gerror.New("仪式名称不能为空")
		}
		if "" == item.UrutanContent {
			return out, gerror.New("仪式内容不能为空")
		}
	}
	// 判断顺序是否重复
	total, err := dao.HajiUrutan.Ctx(ctx).Where(dao.HajiUrutan.Columns().UrutanNo, req.UrutanNo).Count()
	if err != nil {
		return out, err
	}
	if total > 0 {
		return out, gerror.New("此编号已存在，请输入不同的顺序编号。")
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	// 写入表
	err = dao.HajiUrutan.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		urutanData := entity.HajiUrutan{
			UrutanNo:   gconv.Int(req.UrutanNo),
			IconUrl:    req.IconUrl,
			CreateTime: currentTime,
			UpdateTime: currentTime,
		}
		id, err1 := dao.HajiUrutan.Ctx(ctx).Data(urutanData).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 批量插入HajiUrutanLanguage表
		contentList := g.List{}
		for _, item := range req.ContentArr {
			contentList = append(contentList, g.Map{
				dao.HajiUrutanContent.Columns().UrutanId:      gconv.Uint(id),
				dao.HajiUrutanContent.Columns().LanguageId:    item.LanguageType,
				dao.HajiUrutanContent.Columns().UrutanName:    item.UrutanName,
				dao.HajiUrutanContent.Columns().UrutanTime:    item.UrutanTime,
				dao.HajiUrutanContent.Columns().UrutanContent: item.UrutanContent,
				dao.HajiUrutanContent.Columns().CreateTime:    currentTime,
				dao.HajiUrutanContent.Columns().UpdateTime:    currentTime,
			})
		}
		if 0 < len(contentList) {
			_, err1 = dao.HajiUrutanContent.Ctx(ctx).Data(contentList).Insert()
		}
		return err1
	})
	return out, err
}

// 修改朝觐
func (s *sHaji) UrutanManasikEdit(ctx context.Context, req *v1.UrutanManasikEditReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("id不能为空")
	}
	// 获取数据
	var urutan entity.HajiUrutan
	err = dao.HajiUrutan.Ctx(ctx).Where(dao.HajiUrutan.Columns().Id, req.Id).Scan(&urutan)
	if err != nil {
		return out, err
	}
	if 0 >= urutan.Id {
		return out, gerror.New("数据不存在")
	}
	if "" == req.IconUrl {
		return out, gerror.New("图标不能为空")
	}
	if 0 >= req.UrutanNo {
		return out, gerror.New("朝觐顺序不能为空")
	}
	if 0 >= len(req.ContentArr) {
		return out, gerror.New("内容不能为空")
	}
	for _, item := range req.ContentArr {
		if "" == item.UrutanName {
			return out, gerror.New("仪式名称不能为空")
		}
		if "" == item.UrutanContent {
			return out, gerror.New("仪式内容不能为空")
		}
	}
	// 判断顺序是否重复
	total, err := dao.HajiUrutan.Ctx(ctx).
		Where(dao.HajiUrutan.Columns().UrutanNo, req.UrutanNo).
		WhereNot(dao.HajiUrutan.Columns().Id, req.Id).
		Count()
	if err != nil {
		return out, err
	}
	if total > 0 {
		return out, gerror.New("此编号已存在，请输入不同的顺序编号。")
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	// 写入表
	err = dao.HajiUrutan.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		urutanData := g.Map{
			dao.HajiUrutan.Columns().UrutanNo:   gconv.Int(req.UrutanNo),
			dao.HajiUrutan.Columns().IconUrl:    req.IconUrl,
			dao.HajiUrutan.Columns().UpdateTime: currentTime,
		}
		_, err1 = dao.HajiUrutan.Ctx(ctx).Where(dao.HajiUrutan.Columns().Id, req.Id).Data(urutanData).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.HajiUrutanContent.Ctx(ctx).Where(dao.HajiUrutanContent.Columns().UrutanId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 批量插入HajiUrutanLanguage表
		contentList := g.List{}
		for _, item := range req.ContentArr {
			contentList = append(contentList, g.Map{
				dao.HajiUrutanContent.Columns().UrutanId:      gconv.Uint(req.Id),
				dao.HajiUrutanContent.Columns().LanguageId:    item.LanguageType,
				dao.HajiUrutanContent.Columns().UrutanName:    item.UrutanName,
				dao.HajiUrutanContent.Columns().UrutanTime:    item.UrutanTime,
				dao.HajiUrutanContent.Columns().UrutanContent: item.UrutanContent,
				dao.HajiUrutanContent.Columns().CreateTime:    urutan.CreateTime,
				dao.HajiUrutanContent.Columns().UpdateTime:    currentTime,
			})
		}
		if 0 < len(contentList) {
			_, err1 = dao.HajiUrutanContent.Ctx(ctx).Data(contentList).Insert()
		}
		return err1
	})
	return out, err
}

// 获取Urutan Manasik列表
func (s *sHaji) UrutanManasikList(ctx context.Context, req *v1.UrutanManasikListReq) (out *v1.UrutanManasikListRes, err error) {
	out = new(v1.UrutanManasikListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.HajiUrutan.Ctx(ctx)
	// 总数
	out.Total, err = orm.Count()
	if err != nil {
		return
	}
	if out.Total <= 0 {
		out.List = []v1.UrutanManasikListItem{}
		return
	}
	// 获取列表
	var list []entity.HajiUrutan
	err = orm.Page(req.Current, req.PageSize).OrderAsc(dao.HajiUrutan.Columns().UrutanNo).Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.UrutanManasikListItem{}
		return
	}
	// 获取语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	// 提取id
	urutanIds := gutil.ListItemValuesUnique(list, "Id")
	// 获取内容
	var urutanContentList []entity.HajiUrutanContent
	err = dao.HajiUrutanContent.Ctx(ctx).
		WhereIn(dao.HajiUrutanContent.Columns().UrutanId, urutanIds).
		Scan(&urutanContentList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.UrutanManasikListItem{}
		return
	}
	var languageArr = make([]v1.LanguageArrItem, 0)
	// 组成map
	urutanContentMap := make(map[int]map[int]entity.HajiUrutanContent)
	for _, v := range urutanContentList {
		if _, ok := urutanContentMap[gconv.Int(v.UrutanId)]; !ok {
			urutanContentMap[gconv.Int(v.UrutanId)] = make(map[int]entity.HajiUrutanContent)
		}
		urutanContentMap[gconv.Int(v.UrutanId)][gconv.Int(v.LanguageId)] = v
	}
	// 组装数据
	for _, urutan := range list {
		one := v1.UrutanManasikListItem{
			Id:          gconv.Uint(urutan.Id),
			UrutanNo:    gconv.Uint(urutan.UrutanNo),
			IconUrl:     urutan.IconUrl,
			LanguageArr: languageArr,
		}
		urutanContent, ok := urutanContentMap[gconv.Int(urutan.Id)][gconv.Int(currentLang)]
		if ok {
			one.UrutanName = urutanContent.UrutanName
			one.UrutanTime = urutanContent.UrutanTime
		}
		// 组装支持的语言
		for _, language := range []int{consts.Zero, consts.One, consts.Two} {
			// 判断是否存在
			_, ok = urutanContentMap[gconv.Int(urutan.Id)][language]
			var IsSupport int
			if ok {
				IsSupport = consts.One
			}
			one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
				LanguageType:     language,
				LanguageTypeText: consts.GetLangText(language),
				IsSupport:        IsSupport,
			})
		}
		out.List = append(out.List, one)
	}
	return
}

// 获取朝觐详情
func (s *sHaji) UrutanManasikInfo(ctx context.Context, req *v1.UrutanManasikInfoReq) (out *v1.UrutanManasikInfoRes, err error) {
	out = new(v1.UrutanManasikInfoRes)
	// 获取朝觐详情
	var data entity.HajiUrutan
	err = dao.HajiUrutan.Ctx(ctx).Where(dao.HajiUrutan.Columns().Id, req.Id).Scan(&data)
	if err != nil {
		return out, err
	}
	if 0 >= data.Id {
		return out, gerror.New("数据不存在")
	}
	// 获取朝觐内容
	var contentList []entity.HajiUrutanContent
	err = dao.HajiUrutanContent.Ctx(ctx).Where(dao.HajiUrutanContent.Columns().UrutanId, req.Id).Scan(&contentList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	for _, v := range contentList {
		out.ContentArr = append(out.ContentArr, v1.UrutanManasikContentItem{
			LanguageType:  gconv.Int(v.LanguageId),
			UrutanName:    v.UrutanName,
			UrutanTime:    v.UrutanTime,
			UrutanContent: v.UrutanContent,
		})
	}
	out.IconUrl = data.IconUrl
	out.UrutanNo = gconv.Uint(data.UrutanNo)
	out.Id = gconv.Uint(data.Id)
	return
}

// Ringkas doa 列表
func (s *sHaji) DoaRingkasHajiList(ctx context.Context, req *v1.DoaRingkasHajiListReq) (out *v1.DoaRingkasHajiListRes, err error) {
	out = new(v1.DoaRingkasHajiListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.HajiDoaRingkas.Ctx(ctx)
	// 获取总数
	out.Total, err = orm.Count()
	if err != nil {
		out.List = []v1.DoaRingkasListItem{}
		return out, err
	}
	if out.Total <= 0 {
		out.List = []v1.DoaRingkasListItem{}
		return out, nil
	}
	// 获取列表
	var data []entity.HajiDoaRingkas
	err = orm.
		Page(req.Current, req.PageSize).
		OrderAsc(dao.HajiDoaRingkas.Columns().DoaNo).
		Scan(&data)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	for _, item := range data {
		out.List = append(out.List, v1.DoaRingkasListItem{
			DoaName: item.DoaName,
			DoaNo:   item.DoaNo,
			Id:      gconv.Uint(item.Id),
		})
	}
	return out, nil
}

// Ringkas doa 详情
func (s *sHaji) DoaRingkasHajiInfo(ctx context.Context, req *v1.DoaRingkasHajiInfoReq) (out *v1.DoaRingkasHajiInfoRes, err error) {
	out = new(v1.DoaRingkasHajiInfoRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("id不能为空")
	}
	// 查询数据是否存在
	var data entity.HajiDoaRingkas
	err = dao.HajiDoaRingkas.Ctx(ctx).Where(dao.HajiDoaRingkas.Columns().Id, req.Id).Scan(&data)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	if data.Id == 0 {
		return out, gerror.New("数据不存在")
	}
	var list []entity.HajiDoaRingkasContent
	err = dao.HajiDoaRingkasContent.Ctx(ctx).
		Where(dao.HajiDoaRingkasContent.Columns().DoaId, req.Id).
		OrderAsc(dao.HajiDoaRingkasContent.Columns().ContentOrder).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	// 组装数据
	for _, v := range list {
		out.List = append(out.List, v1.DoaRingkasHajiInfoItem{
			Title:          v.Title,
			MuqattaAt:      v.MuqattaAt,
			ArabicText:     v.ArabicText,
			IndonesianText: v.IndonesianText,
			LatinText:      v.LatinText,
		})
	}
	return out, nil
}

// Panjang 列表
func (s *sHaji) DoaPanjangHajiList(ctx context.Context, req *v1.DoaPanjangHajiListReq) (out *v1.DoaPanjangHajiListRes, err error) {
	out = new(v1.DoaPanjangHajiListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	// 获取总数
	out.Total, err = dao.HajiDoaPanjang.Ctx(ctx).Count()
	if err != nil {
		out.List = []v1.DoaPanjangListItem{}
		return out, err
	}
	if out.Total <= 0 {
		out.List = []v1.DoaPanjangListItem{}
		return out, nil
	}
	var list []entity.HajiDoaPanjang
	err = dao.HajiDoaPanjang.Ctx(ctx).
		OrderAsc(dao.HajiDoaPanjang.Columns().DoaNo).
		Page(req.Current, req.PageSize).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return
	}
	for _, v := range list {
		out.List = append(out.List, v1.DoaPanjangListItem{
			BacaanCount: gconv.Int(v.BacaanCount),
			DoaName:     v.DoaName,
			DoaNo:       gconv.Int(v.DoaNo),
			Id:          gconv.Uint(v.Id),
		})
	}
	return out, nil
}

// Panjang Bacaan列表
func (s *sHaji) DoaPanjangHajiBacaanList(ctx context.Context, req *v1.DoaPanjangHajiBacaanListReq) (out *v1.DoaPanjangHajiBacaanListRes, err error) {
	out = new(v1.DoaPanjangHajiBacaanListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	if req.Id == 0 {
		out.List = []v1.DoaPanjangBacaanListItem{}
		return out, gerror.New("doa_id不能为空")
	}
	// 查询总数
	total, err := dao.HajiDoaPanjangBacaan.Ctx(ctx).
		Where(dao.HajiDoaPanjangBacaan.Columns().DoaId, req.Id).
		Count()
	if err != nil {
		return out, err
	}
	if total <= 0 {
		out.List = []v1.DoaPanjangBacaanListItem{}
		return out, nil
	}
	out.Total = total
	// 查询列表
	var list []entity.HajiDoaPanjangBacaan
	err = dao.HajiDoaPanjangBacaan.Ctx(ctx).
		Where(dao.HajiDoaPanjangBacaan.Columns().DoaId, req.Id).
		OrderAsc(dao.HajiDoaPanjangBacaan.Columns().BacaanNo).
		Page(req.Current, req.PageSize).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.DoaPanjangBacaanListItem{}
		return out, err
	}
	for _, v := range list {
		out.List = append(out.List, v1.DoaPanjangBacaanListItem{
			BacaanName: v.BacaanName,
			DoaNo:      v.BacaanNo,
			Id:         gconv.Uint(v.Id),
		})
	}
	return out, nil
}

// Panjang Bacaan列表详情
func (s *sHaji) DoaPanjangHajiBacaanListInfo(ctx context.Context, req *v1.DoaPanjangHajiBacaanListInfoReq) (out *v1.DoaPanjangHajiBacaanListInfoRes, err error) {
	out = new(v1.DoaPanjangHajiBacaanListInfoRes)
	out.Current = req.Current
	out.Offset = req.Offset
	if req.Id == 0 {
		out.List = []v1.DoaPanjangBacaanListInfoItem{}
		return out, gerror.New("BacaanId不能为空")
	}
	orm := dao.HajiDoaPanjangBacaanContent.Ctx(ctx)
	// 获取总数
	out.Total, err = orm.
		Where(dao.HajiDoaPanjangBacaanContent.Columns().BacaanId, req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取Bacaan总数失败")
	}
	if out.Total == 0 {
		out.List = []v1.DoaPanjangBacaanListInfoItem{}
		return out, nil
	}
	// 获取列表
	var list []entity.HajiDoaPanjangBacaanContent
	err = orm.
		Where(dao.HajiDoaPanjangBacaanContent.Columns().BacaanId, req.Id).
		Page(req.Current, req.PageSize).
		OrderAsc(dao.HajiDoaPanjangBacaanContent.Columns().ContentOrder).
		Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.DoaPanjangBacaanListInfoItem{}
		return out, gerror.Wrap(err, "获取列表失败")
	}
	for _, v := range list {
		out.List = append(out.List, v1.DoaPanjangBacaanListInfoItem{
			ArabicText:     v.ArabicText,
			IndonesianText: v.IndonesianText,
			LatinText:      v.LatinText,
			MuqattaAt:      v.MuqattaAt,
			Title:          v.Title,
		})
	}
	return out, nil
}

// Hikmah Haji 列表
func (s *sHaji) HikmahHajiList(ctx context.Context, req *v1.HikmahHajiListReq) (out *v1.HikmahHajiListRes, err error) {
	out = new(v1.HikmahHajiListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.HajiHikmah.Ctx(ctx)
	// 获取总数
	total, err := orm.Count()
	if err != nil {
		out.List = []v1.HikmahHajiListItem{}
		return out, err
	}
	if total <= 0 {
		out.List = []v1.HikmahHajiListItem{}
		return out, nil
	}
	out.Total = total
	var list []entity.HajiHikmah
	err = orm.Page(req.Current, req.PageSize).OrderAsc(dao.HajiHikmah.Columns().SortOrder).Scan(&list)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.HikmahHajiListItem{}
		return out, err
	}
	// 获取语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	// 提取文章id
	articleIds := gutil.ListItemValuesUnique(list, "ArticleId")
	type article struct {
		ArticleId uint   `json:"article_id"`
		Name      string `json:"name"`
	}
	var articleList []article
	err = dao.NewsArticleLanguage.Ctx(ctx).
		WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, articleIds).
		Where(dao.NewsArticleLanguage.Columns().LanguageId, currentLang).
		Scan(&articleList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.HikmahHajiListItem{}
		return out, err
	}
	var articleListMap = make(map[uint]string)
	for _, v := range articleList {
		articleListMap[v.ArticleId] = v.Name
	}
	// 提取id
	ids := gutil.ListItemValuesUnique(list, "Id")
	var languageList []entity.HajiHikmahLanguages
	err = dao.HajiHikmahLanguages.Ctx(ctx).Where(dao.HajiHikmahLanguages.Columns().HikmahId, ids).Scan(&languageList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.HikmahHajiListItem{}
		return out, err
	}
	var languageListMap = make(map[uint]map[int]entity.HajiHikmahLanguages)
	for _, v := range languageList {
		if _, ok := languageListMap[gconv.Uint(v.HikmahId)]; !ok {
			languageListMap[gconv.Uint(v.HikmahId)] = make(map[int]entity.HajiHikmahLanguages)
		}
		languageListMap[gconv.Uint(v.HikmahId)][gconv.Int(v.LanguageId)] = v
	}
	// 计算序号开始值
	serialNumber := (req.Current-1)*req.PageSize + 1
	for _, v := range list {
		articleName, ok := articleListMap[gconv.Uint(v.ArticleId)]
		if !ok {
			articleName = ""
		}
		var title string
		item, ok := languageListMap[gconv.Uint(v.Id)][gconv.Int(currentLang)]
		if ok {
			title = item.Title
		}
		one := v1.HikmahHajiListItem{
			Id:          gconv.Uint(v.Id),
			Title:       title,
			ArticleId:   gconv.Uint(v.ArticleId),
			ArticleName: articleName,
			LanguageArr: []v1.LanguageArrItem{},
			SerialNum:   serialNumber,
			SortOrder:   gconv.Int(v.SortOrder),
		}
		var IsZh, IsEn, IsId int
		_, ok = languageListMap[gconv.Uint(v.Id)][consts.Zero]
		if ok {
			IsZh = consts.One
		}
		_, ok = languageListMap[gconv.Uint(v.Id)][consts.One]
		if ok {
			IsEn = consts.One
		}
		_, ok = languageListMap[gconv.Uint(v.Id)][consts.Two]
		if ok {
			IsId = consts.One
		}
		// 支持的语言
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Zero,
			LanguageTypeText: consts.ManLangZhStr,
			IsSupport:        IsZh,
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: consts.ManLangEnStr,
			IsSupport:        IsEn,
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: consts.ManLangIdStr,
			IsSupport:        IsId,
		})
		out.List = append(out.List, one)
		serialNumber++
	}
	return out, nil
}

// Hikmah Haji 详情
func (s *sHaji) HikmahHajiInfo(ctx context.Context, req *v1.HikmahHajiInfoReq) (out *v1.HikmahHajiInfoRes, err error) {
	out = new(v1.HikmahHajiInfoRes)
	var data entity.HajiHikmah
	err = dao.HajiHikmah.Ctx(ctx).Where(dao.HajiHikmah.Columns().Id, req.Id).Scan(&data)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	var languageList []entity.HajiHikmahLanguages
	err = dao.HajiHikmahLanguages.Ctx(ctx).Where(dao.HajiHikmahLanguages.Columns().HikmahId, req.Id).Scan(&languageList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	var titleArr = make([]v1.TitleArrItem, 0)
	for _, v := range languageList {
		titleArr = append(titleArr, v1.TitleArrItem{
			LanguageType: gconv.Int(v.LanguageId),
			Title:        v.Title,
		})
	}
	// 获取文章分类
	type ArticleCategory struct {
		CategoryId uint `json:"category_id" dc:"关联文章分类id"`
	}
	var articleCategory ArticleCategory
	err = dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().Id, req.Id).Scan(&articleCategory)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	out.Id = req.Id
	out.TitleArr = titleArr
	out.SortOrder = gconv.Int(data.SortOrder)
	out.ArticleId = gconv.Uint(data.ArticleId)
	out.ArticleCategoryId = gconv.Uint(articleCategory.CategoryId)

	return out, nil
}

// Hikmah Haji 新增
func (s *sHaji) HikmahHajiAdd(ctx context.Context, req *v1.HikmahHajiAddReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= req.SortOrder || req.SortOrder > 9999 {
		return out, gerror.New("排序必须大于0且小于9999")
	}
	if len(req.TitleArr) <= 0 {
		return out, gerror.New("标题列表不能为空")
	}
	if 0 >= req.ArticleId {
		return out, gerror.New("关联文章id不能为空")
	}
	for _, item := range req.TitleArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return out, gerror.New("语言类型错误")
		}
		if "" == item.Title {
			return out, gerror.New("标题不能为空")
		}
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	var hajiHikmahLanguagesData = make([]entity.HajiHikmahLanguages, 0)
	err = dao.HajiHikmah.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		data := entity.HajiHikmah{
			ArticleId:  gconv.Uint64(req.ArticleId),
			SortOrder:  gconv.Uint(req.SortOrder),
			CreateTime: currentTime,
			UpdateTime: currentTime,
		}
		id, err1 := dao.HajiHikmah.Ctx(ctx).Data(data).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 写入HajiHikmahLanguage表
		for _, languageItem := range req.TitleArr {
			hajiHikmahLanguagesData = append(hajiHikmahLanguagesData, entity.HajiHikmahLanguages{
				HikmahId:   gconv.Uint64(id),
				LanguageId: gconv.Uint(languageItem.LanguageType),
				Title:      languageItem.Title,
				CreateTime: currentTime,
				UpdateTime: currentTime,
			})
		}
		_, err1 = dao.HajiHikmahLanguages.Ctx(ctx).Data(hajiHikmahLanguagesData).Insert()
		return err1
	})

	return out, nil
}

// Hikmah Haji 编辑
func (s *sHaji) HikmahHajiEdit(ctx context.Context, req *v1.HikmahHajiEditReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("id不能为空")
	}
	if 0 >= req.SortOrder || req.SortOrder > 9999 {
		return out, gerror.New("排序必须大于0且小于9999")
	}
	if len(req.TitleArr) <= 0 {
		return out, gerror.New("标题列表不能为空")
	}
	if 0 >= req.ArticleId {
		return out, gerror.New("关联文章id不能为空")
	}
	for _, item := range req.TitleArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return out, gerror.New("语言类型错误")
		}
		if "" == item.Title {
			return out, gerror.New("标题不能为空")
		}
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	var hajiHikmahLanguagesData = make([]entity.HajiHikmahLanguages, 0)
	err = dao.HajiHikmah.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		data := g.Map{
			dao.HajiHikmah.Columns().ArticleId:  req.ArticleId,
			dao.HajiHikmah.Columns().SortOrder:  req.SortOrder,
			dao.HajiHikmah.Columns().UpdateTime: currentTime,
		}
		_, err1 = dao.HajiHikmah.Ctx(ctx).Where(dao.HajiHikmah.Columns().Id, req.Id).Data(data).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.HajiHikmahLanguages.Ctx(ctx).Where(dao.HajiHikmahLanguages.Columns().HikmahId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 写入HajiHikmahLanguage表
		for _, languageItem := range req.TitleArr {
			hajiHikmahLanguagesData = append(hajiHikmahLanguagesData, entity.HajiHikmahLanguages{
				HikmahId:   gconv.Uint64(req.Id),
				LanguageId: gconv.Uint(languageItem.LanguageType),
				Title:      languageItem.Title,
				CreateTime: currentTime,
				UpdateTime: currentTime,
			})
		}
		_, err1 = dao.HajiHikmahLanguages.Ctx(ctx).Data(hajiHikmahLanguagesData).Insert()
		return err1
	})

	return out, nil
}

// Hikmah Haji 删除
func (s *sHaji) HikmahHajiDelete(ctx context.Context, req *v1.HikmahHajiDeleteReq) (out *v1.EmptyDataRes, err error) {
	out = new(v1.EmptyDataRes)
	// 参数校验
	if 0 >= len(req.Ids) {
		return out, gerror.New("id不能为空")
	}
	err = dao.HajiHikmah.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = dao.HajiHikmah.Ctx(ctx).WhereIn(dao.HajiHikmah.Columns().Id, req.Ids).Delete()
		if err1 != nil {
			return err1
		}
		// 物理删除
		_, err1 = dao.HajiHikmahLanguages.Ctx(ctx).WhereIn(dao.HajiHikmahLanguages.Columns().HikmahId, req.Ids).Delete()
		return err1
	})

	return out, nil
}
