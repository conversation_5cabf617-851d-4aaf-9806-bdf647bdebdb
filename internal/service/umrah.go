// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IUmrah interface {
		// 新增朝觐
		UrutanManasikUmrahAdd(ctx context.Context, req *v1.UrutanManasikUmrahAddReq) (out *v1.EmptyDataRes, err error)
		// 修改朝觐
		UrutanManasikUmrahEdit(ctx context.Context, req *v1.UrutanManasikUmrahEditReq) (out *v1.EmptyDataRes, err error)
		// 获取Urutan Manasik列表
		UrutanManasikUmrahList(ctx context.Context, req *v1.UrutanManasikUmrahListReq) (out *v1.UrutanManasikUmrahListRes, err error)
		// 获取朝觐详情
		UrutanManasikUmrahInfo(ctx context.Context, req *v1.UrutanManasikUmrahInfoReq) (out *v1.UrutanManasikUmrahInfoRes, err error)
		// Ringkas doa 列表
		DoaRingkasUmrahList(ctx context.Context, req *v1.DoaRingkasUmrahListReq) (out *v1.DoaRingkasUmrahListRes, err error)
		// Ringkas doa 详情
		DoaRingkasUmrahInfo(ctx context.Context, req *v1.DoaRingkasUmrahInfoReq) (out *v1.DoaRingkasUmrahInfoRes, err error)
		// Panjang 列表
		DoaPanjangUmrahList(ctx context.Context, req *v1.DoaPanjangUmrahListReq) (out *v1.DoaPanjangUmrahListRes, err error)
		// Panjang Bacaan列表
		DoaPanjangUmrahBacaanList(ctx context.Context, req *v1.DoaPanjangUmrahBacaanListReq) (out *v1.DoaPanjangUmrahBacaanListRes, err error)
		// Panjang Bacaan列表详情
		DoaPanjangUmrahBacaanListInfo(ctx context.Context, req *v1.DoaPanjangUmrahBacaanListInfoReq) (out *v1.DoaPanjangUmrahBacaanListInfoRes, err error)
		// Hikmah Umrah 列表
		HikmahUmrahList(ctx context.Context, req *v1.HikmahUmrahListReq) (out *v1.HikmahUmrahListRes, err error)
		// Hikmah Umrah 详情
		HikmahUmrahInfo(ctx context.Context, req *v1.HikmahUmrahInfoReq) (out *v1.HikmahUmrahInfoRes, err error)
		// Hikmah Umrah 新增
		HikmahUmrahAdd(ctx context.Context, req *v1.HikmahUmrahAddReq) (out *v1.EmptyDataRes, err error)
		// Hikmah Umrah 编辑
		HikmahUmrahEdit(ctx context.Context, req *v1.HikmahUmrahEditReq) (out *v1.EmptyDataRes, err error)
		// Hikmah Umrah 删除
		HikmahUmrahDelete(ctx context.Context, req *v1.HikmahUmrahDeleteReq) (out *v1.EmptyDataRes, err error)
	}
)

var (
	localUmrah IUmrah
)

func Umrah() IUmrah {
	if localUmrah == nil {
		panic("implement not found for interface IUmrah, forgot register?")
	}
	return localUmrah
}

func RegisterUmrah(i IUmrah) {
	localUmrah = i
}
