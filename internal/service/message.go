// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IMessage interface {
		Add(ctx context.Context, req *v1.MessageCreateReq) (res *v1.MessageCreateRes, err error)
		MessageEdit(ctx context.Context, req *v1.MessageEditReq) (res *v1.MessageEditRes, err error)
		MessageDelete(ctx context.Context, req *v1.MessageDeleteReq) (res *v1.MessageDeleteRes, err error)
		MessageOne(ctx context.Context, req *v1.MessageOneReq) (res *v1.MessageOneRes, err error)
		MessageList(ctx context.Context, req *v1.MessageListReq) (res *v1.MessageListRes, err error)
	}
)

var (
	localMessage IMessage
)

func Message() IMessage {
	if localMessage == nil {
		panic("implement not found for interface IMessage, forgot register?")
	}
	return localMessage
}

func RegisterMessage(i IMessage) {
	localMessage = i
}
