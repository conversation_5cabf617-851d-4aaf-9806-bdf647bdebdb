// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IHaji interface {
		// 新增朝觐
		UrutanManasikAdd(ctx context.Context, req *v1.UrutanManasikAddReq) (out *v1.EmptyDataRes, err error)
		// 修改朝觐
		UrutanManasikEdit(ctx context.Context, req *v1.UrutanManasikEditReq) (out *v1.EmptyDataRes, err error)
		// 获取Urutan Manasik列表
		UrutanManasikList(ctx context.Context, req *v1.UrutanManasikListReq) (out *v1.UrutanManasikListRes, err error)
		// 获取朝觐详情
		UrutanManasikInfo(ctx context.Context, req *v1.UrutanManasikInfoReq) (out *v1.UrutanManasikInfoRes, err error)
		// Ringkas doa 列表
		DoaRingkasHajiList(ctx context.Context, req *v1.DoaRingkasHajiListReq) (out *v1.DoaRingkasHajiListRes, err error)
		// Ringkas doa 详情
		DoaRingkasHajiInfo(ctx context.Context, req *v1.DoaRingkasHajiInfoReq) (out *v1.DoaRingkasHajiInfoRes, err error)
		// Panjang 列表
		DoaPanjangHajiList(ctx context.Context, req *v1.DoaPanjangHajiListReq) (out *v1.DoaPanjangHajiListRes, err error)
		// Panjang Bacaan列表
		DoaPanjangHajiBacaanList(ctx context.Context, req *v1.DoaPanjangHajiBacaanListReq) (out *v1.DoaPanjangHajiBacaanListRes, err error)
		// Panjang Bacaan列表详情
		DoaPanjangHajiBacaanListInfo(ctx context.Context, req *v1.DoaPanjangHajiBacaanListInfoReq) (out *v1.DoaPanjangHajiBacaanListInfoRes, err error)
		// Hikmah Haji 列表
		HikmahHajiList(ctx context.Context, req *v1.HikmahHajiListReq) (out *v1.HikmahHajiListRes, err error)
		// Hikmah Haji 详情
		HikmahHajiInfo(ctx context.Context, req *v1.HikmahHajiInfoReq) (out *v1.HikmahHajiInfoRes, err error)
		// Hikmah Haji 新增
		HikmahHajiAdd(ctx context.Context, req *v1.HikmahHajiAddReq) (out *v1.EmptyDataRes, err error)
		// Hikmah Haji 编辑
		HikmahHajiEdit(ctx context.Context, req *v1.HikmahHajiEditReq) (out *v1.EmptyDataRes, err error)
		// Hikmah Haji 删除
		HikmahHajiDelete(ctx context.Context, req *v1.HikmahHajiDeleteReq) (out *v1.EmptyDataRes, err error)
	}
)

var (
	localHaji IHaji
)

func Haji() IHaji {
	if localHaji == nil {
		panic("implement not found for interface IHaji, forgot register?")
	}
	return localHaji
}

func RegisterHaji(i IHaji) {
	localHaji = i
}
