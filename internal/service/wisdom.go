// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IWisdom interface {
		WisdomCateAdd(ctx context.Context, req *v1.WisdomCateCreateReq) (res *v1.WisdomCateCreateRes, err error)
		WisdomCateEdit(ctx context.Context, req *v1.WisdomCateEditReq) (res *v1.WisdomCateEditRes, err error)
		WisdomCateOne(ctx context.Context, req *v1.WisdomCateOneReq) (res *v1.WisdomCateOneRes, err error)
		WisdomCateDelete(ctx context.Context, req *v1.WisdomCateDeleteReq) (res *v1.WisdomCateDeleteRes, err error)
		WisdomCateList(ctx context.Context, req *v1.WisdomCateListReq) (res *v1.WisdomCateListRes, err error)
		WisdomAdd(ctx context.Context, req *v1.WisdomCreateReq) (res *v1.WisdomCreateRes, err error)
		WisdomEdit(ctx context.Context, req *v1.WisdomEditReq) (res *v1.WisdomEditRes, err error)
		WisdomDelete(ctx context.Context, req *v1.WisdomDeleteReq) (res *v1.WisdomDeleteRes, err error)
		WisdomOne(ctx context.Context, req *v1.WisdomOneReq) (res *v1.WisdomOneRes, err error)
		WisdomList(ctx context.Context, req *v1.WisdomListReq) (res *v1.WisdomListRes, err error)
		WisdomCateAll(ctx context.Context, req *v1.WisdomCateAllReq) (res *v1.WisdomCateAllRes, err error)
		Publish(ctx context.Context, req *v1.WisdomPublishReq) (res *v1.WisdomPublishRes, err error)
	}
)

var (
	localWisdom IWisdom
)

func Wisdom() IWisdom {
	if localWisdom == nil {
		panic("implement not found for interface IWisdom, forgot register?")
	}
	return localWisdom
}

func RegisterWisdom(i IWisdom) {
	localWisdom = i
}
